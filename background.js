// Background script for TradingView Ticker Extractor
// Handles extension lifecycle and communication between components

chrome.runtime.onInstalled.addListener((details) => {
    console.log('TradingView Ticker Extractor installed:', details.reason);
    
    if (details.reason === 'install') {
        // Show welcome notification
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'images/icon48.png',
            title: 'TradingView Ticker Extractor',
            message: 'Extension installed! Click the icon on any TradingView page to extract ticker data.'
        });
    }
});

// Handle extension icon click
chrome.action.onClicked.addListener(async (tab) => {
    // Check if we're on a TradingView page
    if (!tab.url.includes('tradingview.com')) {
        // Show notification if not on TradingView
        chrome.notifications.create({
            type: 'basic',
            iconUrl: 'images/icon48.png',
            title: 'TradingView Required',
            message: 'Please navigate to a TradingView page to use this extension.'
        });
        return;
    }
    
    // The popup will handle the rest
});

// Listen for tab updates to refresh data
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
    // Only process complete page loads on TradingView
    if (changeInfo.status === 'complete' && 
        tab.url && 
        tab.url.includes('tradingview.com')) {
        
        // Clear any old data when navigating to a new chart
        chrome.storage.local.remove(['tradingViewData']);
        
        console.log('TradingView page loaded, ready for data extraction');
    }
});

// Handle messages from content scripts and popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getData') {
        // Get stored data
        chrome.storage.local.get(['tradingViewData'], (result) => {
            sendResponse(result.tradingViewData || null);
        });
        return true; // Keep message channel open for async response
    }
    
    if (request.action === 'storeData') {
        // Store data from content script
        chrome.storage.local.set({
            tradingViewData: request.data,
            lastUpdated: Date.now()
        });
        sendResponse({ success: true });
    }
});

// Clean up old data periodically
chrome.alarms.create('cleanup', { periodInMinutes: 60 });

chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === 'cleanup') {
        chrome.storage.local.get(['lastUpdated'], (result) => {
            const lastUpdated = result.lastUpdated || 0;
            const oneHourAgo = Date.now() - (60 * 60 * 1000);
            
            // Clear data if it's older than 1 hour
            if (lastUpdated < oneHourAgo) {
                chrome.storage.local.remove(['tradingViewData', 'lastUpdated']);
                console.log('Cleaned up old TradingView data');
            }
        });
    }
});

// Handle extension errors
chrome.runtime.onStartup.addListener(() => {
    console.log('TradingView Ticker Extractor started');
});

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        // Export any functions you want to test
    };
}
