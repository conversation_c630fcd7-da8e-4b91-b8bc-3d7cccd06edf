// Content script for TradingView Ticker Extractor
// This script runs on TradingView pages to extract ticker and timeframe data

(function() {
    'use strict';

    let lastData = {};
    let observer = null;

    // Initialize the content script
    init();

    function init() {
        console.log('TradingView Ticker Extractor: Content script loaded');
        
        // Wait for page to be fully loaded
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startExtraction);
        } else {
            startExtraction();
        }
    }

    function startExtraction() {
        // Start monitoring for changes
        startObserver();
        
        // Initial extraction
        setTimeout(() => {
            extractAndStore();
        }, 2000); // Wait 2 seconds for TradingView to load
    }

    function startObserver() {
        // Create a mutation observer to watch for changes in the DOM
        observer = new MutationObserver((mutations) => {
            let shouldUpdate = false;
            
            mutations.forEach((mutation) => {
                // Check if any relevant elements changed
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const target = mutation.target;
                    
                    // Check if the change affects ticker or timeframe elements
                    if (target.closest && (
                        target.closest('[data-name="legend-source-title"]') ||
                        target.closest('[data-name="time-interval"]') ||
                        target.closest('.js-symbol-text') ||
                        target.closest('.js-time-interval')
                    )) {
                        shouldUpdate = true;
                    }
                }
            });
            
            if (shouldUpdate) {
                // Debounce updates
                clearTimeout(window.tickerExtractorTimeout);
                window.tickerExtractorTimeout = setTimeout(extractAndStore, 500);
            }
        });

        // Start observing
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true
        });
    }

    function extractAndStore() {
        const data = extractTradingViewData();
        
        if (data && (
            data.ticker !== lastData.ticker ||
            data.timeframe !== lastData.timeframe ||
            data.price !== lastData.price
        )) {
            lastData = { ...data };
            
            // Store data for popup to access
            chrome.storage.local.set({
                tradingViewData: data,
                lastUpdated: Date.now()
            });
            
            console.log('TradingView data extracted:', data);
        }
    }

    function extractTradingViewData() {
        try {
            const data = {
                ticker: null,
                timeframe: null,
                exchange: null,
                price: null,
                url: window.location.href
            };

            // Extract ticker symbol - multiple selectors for different TradingView layouts
            const tickerSelectors = [
                '[data-name="legend-source-title"]',
                '.js-symbol-text',
                '[class*="symbolName"]',
                '[data-name="legend-source-item"] [class*="title"]',
                '.chart-markup-table .js-symbol-text',
                '[class*="symbol-name"]',
                '.symbol-name-text',
                '[data-symbol-full]'
            ];

            for (const selector of tickerSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    let text = element.textContent?.trim() || element.getAttribute('data-symbol-full');
                    if (text) {
                        data.ticker = text;
                        break;
                    }
                }
            }

            // Extract timeframe
            const timeframeSelectors = [
                '[data-name="time-interval"]',
                '.js-time-interval',
                '[class*="timeframe"]',
                '[data-name="time-interval-dialog-button"]',
                '.interval-item.active',
                '[class*="interval"][class*="active"]',
                '[data-value][class*="interval"]'
            ];

            for (const selector of timeframeSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    let text = element.textContent?.trim() || element.getAttribute('data-value');
                    if (text) {
                        data.timeframe = text;
                        break;
                    }
                }
            }

            // Extract exchange (often part of ticker)
            if (data.ticker && data.ticker.includes(':')) {
                const parts = data.ticker.split(':');
                data.exchange = parts[0];
                data.ticker = parts[1];
            }

            // Extract current price
            const priceSelectors = [
                '[data-name="legend-source-item"] [class*="valueValue"]',
                '.js-symbol-last',
                '[class*="last-price"]',
                '[data-name="legend-source-item"] .js-symbol-last',
                '[class*="price-value"]',
                '[data-name="legend-source-item"] .js-symbol-last-value'
            ];

            for (const selector of priceSelectors) {
                const element = document.querySelector(selector);
                if (element && element.textContent?.trim()) {
                    data.price = element.textContent.trim();
                    break;
                }
            }

            // Fallback: try to get data from URL
            if (!data.ticker) {
                const urlMatch = window.location.pathname.match(/\/chart\/([^\/]+)/);
                if (urlMatch) {
                    data.ticker = decodeURIComponent(urlMatch[1]);
                }
            }

            // Additional fallback for ticker from page title
            if (!data.ticker) {
                const titleMatch = document.title.match(/^([A-Z0-9:]+)/);
                if (titleMatch) {
                    data.ticker = titleMatch[1];
                }
            }

            return data;
        } catch (error) {
            console.error('Error extracting TradingView data:', error);
            return null;
        }
    }

    // Listen for messages from popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        if (request.action === 'extractData') {
            const data = extractTradingViewData();
            sendResponse(data);
        }
    });

    // Cleanup on page unload
    window.addEventListener('beforeunload', () => {
        if (observer) {
            observer.disconnect();
        }
    });

})();
