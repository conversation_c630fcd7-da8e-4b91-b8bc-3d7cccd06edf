* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    width: 350px;
    min-height: 400px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

.container {
    padding: 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.header {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.icon {
    width: 32px;
    height: 32px;
    margin-right: 12px;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.content {
    flex: 1;
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.data-section {
    margin-bottom: 20px;
}

.data-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.data-item:last-child {
    border-bottom: none;
}

.data-item label {
    font-weight: 600;
    color: #555;
    font-size: 14px;
}

.data-value {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 80px;
    text-align: center;
}

.status-section {
    margin-bottom: 20px;
}

.status {
    text-align: center;
    padding: 10px;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
}

.status.loading {
    background: #e3f2fd;
    color: #1976d2;
}

.status.success {
    background: #e8f5e8;
    color: #2e7d32;
}

.status.error {
    background: #ffebee;
    color: #c62828;
}

.actions {
    display: flex;
    gap: 10px;
}

.btn {
    flex: 1;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn:not(.secondary) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn.secondary {
    background: #f8f9fa;
    color: #555;
    border: 1px solid #dee2e6;
}

.btn.secondary:hover {
    background: #e9ecef;
}

.footer {
    margin-top: 15px;
    text-align: center;
}

.footer small {
    color: rgba(255, 255, 255, 0.8);
    font-size: 12px;
}

/* Loading animation */
.loading-dots::after {
    content: '';
    animation: loading-dots 1.5s infinite;
}

@keyframes loading-dots {
    0%, 20% { content: '.'; }
    40% { content: '..'; }
    60%, 100% { content: '...'; }
}
