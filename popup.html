<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Ticker Extractor</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="images/icon48.png" alt="Extension Icon" class="icon">
            <h1>TradingView Data</h1>
        </div>
        
        <div class="content">
            <div class="data-section">
                <div class="data-item">
                    <label>Ticker Symbol:</label>
                    <span id="ticker-symbol" class="data-value">Loading...</span>
                </div>
                
                <div class="data-item">
                    <label>Timeframe:</label>
                    <span id="timeframe" class="data-value">Loading...</span>
                </div>
                
                <div class="data-item">
                    <label>Exchange:</label>
                    <span id="exchange" class="data-value">Loading...</span>
                </div>
                
                <div class="data-item">
                    <label>Current Price:</label>
                    <span id="current-price" class="data-value">Loading...</span>
                </div>
            </div>
            
            <div class="status-section">
                <div id="status" class="status">Extracting data...</div>
            </div>
            
            <div class="actions">
                <button id="refresh-btn" class="btn">Refresh Data</button>
                <button id="copy-btn" class="btn secondary">Copy Data</button>
            </div>
        </div>
        
        <div class="footer">
            <small>Click refresh if data doesn't load automatically</small>
        </div>
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
