// Popup script for TradingView Ticker Extractor
document.addEventListener('DOMContentLoaded', function() {
    const tickerElement = document.getElementById('ticker-symbol');
    const timeframeElement = document.getElementById('timeframe');
    const exchangeElement = document.getElementById('exchange');
    const priceElement = document.getElementById('current-price');
    const statusElement = document.getElementById('status');
    const refreshBtn = document.getElementById('refresh-btn');
    const copyBtn = document.getElementById('copy-btn');

    // Initialize popup
    init();

    // Event listeners
    refreshBtn.addEventListener('click', refreshData);
    copyBtn.addEventListener('click', copyData);

    async function init() {
        try {
            // Check if we're on a TradingView page
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('tradingview.com')) {
                showError('Please navigate to a TradingView page first');
                return;
            }

            // Extract data from the page
            await extractData();
        } catch (error) {
            console.error('Initialization error:', error);
            showError('Failed to initialize extension');
        }
    }

    async function extractData() {
        try {
            setStatus('Extracting data...', 'loading');
            
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // Inject content script and get data
            const results = await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                function: extractTradingViewData
            });

            if (results && results[0] && results[0].result) {
                const data = results[0].result;
                updateUI(data);
                setStatus('Data extracted successfully', 'success');
            } else {
                showError('No data found. Make sure you\'re on a TradingView chart page.');
            }
        } catch (error) {
            console.error('Data extraction error:', error);
            showError('Failed to extract data from page');
        }
    }

    function updateUI(data) {
        tickerElement.textContent = data.ticker || 'N/A';
        timeframeElement.textContent = data.timeframe || 'N/A';
        exchangeElement.textContent = data.exchange || 'N/A';
        priceElement.textContent = data.price || 'N/A';
    }

    function setStatus(message, type) {
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
    }

    function showError(message) {
        setStatus(message, 'error');
        tickerElement.textContent = 'Error';
        timeframeElement.textContent = 'Error';
        exchangeElement.textContent = 'Error';
        priceElement.textContent = 'Error';
    }

    async function refreshData() {
        refreshBtn.textContent = 'Refreshing...';
        refreshBtn.disabled = true;
        
        await extractData();
        
        refreshBtn.textContent = 'Refresh Data';
        refreshBtn.disabled = false;
    }

    async function copyData() {
        const ticker = tickerElement.textContent;
        const timeframe = timeframeElement.textContent;
        const exchange = exchangeElement.textContent;
        const price = priceElement.textContent;

        const dataText = `Ticker: ${ticker}\nTimeframe: ${timeframe}\nExchange: ${exchange}\nPrice: ${price}`;
        
        try {
            await navigator.clipboard.writeText(dataText);
            copyBtn.textContent = 'Copied!';
            setTimeout(() => {
                copyBtn.textContent = 'Copy Data';
            }, 2000);
        } catch (error) {
            console.error('Copy failed:', error);
            copyBtn.textContent = 'Copy Failed';
            setTimeout(() => {
                copyBtn.textContent = 'Copy Data';
            }, 2000);
        }
    }
});

// Function to be injected into the TradingView page
function extractTradingViewData() {
    try {
        const data = {
            ticker: null,
            timeframe: null,
            exchange: null,
            price: null
        };

        // Extract ticker symbol - multiple selectors for different TradingView layouts
        const tickerSelectors = [
            '[data-name="legend-source-title"]',
            '.js-symbol-text',
            '[class*="symbolName"]',
            '[data-name="legend-source-item"] [class*="title"]',
            '.chart-markup-table .js-symbol-text',
            '[class*="symbol-name"]'
        ];

        for (const selector of tickerSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                data.ticker = element.textContent.trim();
                break;
            }
        }

        // Extract timeframe - enhanced selectors
        const timeframeSelectors = [
            '[data-name="time-interval"]',
            '.js-time-interval',
            '[class*="timeframe"]',
            '[data-name="time-interval-dialog-button"]',
            '.interval-item.active',
            '[class*="interval"][class*="active"]',
            '[data-value][class*="interval"]',
            '.chart-container-border [class*="interval"]',
            '[data-name="intervals-toolbar"] [class*="active"]',
            '.toolbar-button[class*="active"][data-name*="interval"]',
            '.resolution-item.active',
            '[data-tooltip*="interval"] [class*="active"]',
            '.time-interval-selector .active',
            '[aria-label*="interval"] [class*="selected"]'
        ];

        for (const selector of timeframeSelectors) {
            const element = document.querySelector(selector);
            if (element) {
                let text = element.textContent?.trim() ||
                          element.getAttribute('data-value') ||
                          element.getAttribute('data-interval') ||
                          element.getAttribute('aria-label');
                if (text && text !== 'undefined' && text !== '') {
                    // Clean up timeframe text
                    text = text.replace(/[^\w\d]/g, '').toUpperCase();
                    if (text.match(/^\d+[MHDW]?$/)) {
                        data.timeframe = text;
                        break;
                    }
                }
            }
        }

        // Fallback: try to extract from URL parameters
        if (!data.timeframe) {
            const urlParams = new URLSearchParams(window.location.search);
            const interval = urlParams.get('interval') || urlParams.get('timeframe');
            if (interval) {
                data.timeframe = interval;
            }
        }

        // Extract exchange - enhanced detection
        if (data.ticker && data.ticker.includes(':')) {
            const parts = data.ticker.split(':');
            data.exchange = parts[0];
            data.ticker = parts[1];
        } else {
            // Try to find exchange from other elements
            const exchangeSelectors = [
                '[data-name="legend-source-item"] [class*="exchange"]',
                '.js-symbol-exchange',
                '[class*="exchange-name"]',
                '[data-exchange]',
                '.symbol-info [class*="exchange"]'
            ];

            for (const selector of exchangeSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    let text = element.textContent?.trim() || element.getAttribute('data-exchange');
                    if (text && text !== 'undefined' && text !== '') {
                        data.exchange = text;
                        break;
                    }
                }
            }

            // Fallback: extract from URL or page context
            if (!data.exchange) {
                const urlMatch = window.location.pathname.match(/\/chart\/([^:]+):/);
                if (urlMatch) {
                    data.exchange = urlMatch[1];
                }
            }
        }

        // Extract current price
        const priceSelectors = [
            '[data-name="legend-source-item"] [class*="valueValue"]',
            '.js-symbol-last',
            '[class*="last-price"]',
            '[data-name="legend-source-item"] .js-symbol-last',
            '[class*="price-value"]'
        ];

        for (const selector of priceSelectors) {
            const element = document.querySelector(selector);
            if (element && element.textContent.trim()) {
                data.price = element.textContent.trim();
                break;
            }
        }

        // Fallback: try to get data from URL
        if (!data.ticker) {
            const urlMatch = window.location.pathname.match(/\/chart\/([^\/]+)/);
            if (urlMatch) {
                data.ticker = urlMatch[1];
            }
        }

        return data;
    } catch (error) {
        console.error('Error extracting TradingView data:', error);
        return null;
    }
}
